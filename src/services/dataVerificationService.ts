import { captureException } from "@sentry/node";
import { entitiesConfig, investmentUniverseConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";
import mongoose from "mongoose";
import { Payout } from "../models/Payout";
import {
  ESTIMATED_WORK_DAYS_TO_RECEIVE_FUNDS_AFTER_COLLECTION,
  WORK_DAYS_TO_CREATE_DEPOSITS_BEFORE_AUTOMATION_DATE
} from "../configs/directDebitConfig";
import { ProviderEnum } from "../configs/providersConfig";
import logger from "../external-services/loggerService";
import MailchimpService, { MailchimpUserMergeFieldType } from "../external-services/mailchimpService";
import {
  BalanceType,
  HoldingValuationType,
  ValuationType,
  WealthkernelService
} from "../external-services/wealthkernelService";
import { Automation, AutomationDocument, RebalanceAutomation, TopUpAutomation } from "../models/Automation";
import { BankAccount } from "../models/BankAccount";
import { ContentEntry } from "../models/ContentEntry";
import { CreditTicket } from "../models/CreditTicket";
import { DailySummarySnapshot } from "../models/DailySummarySnapshot";
import { HoldingsType, PortfolioDocument, SavingType } from "../models/Portfolio";
import { Reward, RewardDocument } from "../models/Reward";
import { Subscription } from "../models/Subscription";
import {
  AssetTransaction,
  AssetTransactionDocument,
  CashbackTransactionDocument,
  ChargeTransaction,
  ChargeTransactionDocument,
  DepositCashTransaction,
  SavingsDividendTransaction,
  SavingsDividendTransactionDocument,
  SavingsTopupTransaction,
  SavingsWithdrawalTransaction,
  Transaction,
  TransactionDocument,
  WithdrawalCashTransaction,
  WithdrawalCashTransactionDocument
} from "../models/Transaction";
import { KycStatusEnum, UserDocument, UserTypeEnum } from "../models/User";
import { UserDataRequestDocument } from "../models/UserDataRequest";
import { DepositMethodEnum, WithdrawalMethodEnum } from "../types/transactions";
import { getCachedDataWithFallback } from "../utils/cacheUtil";
import DateUtil from "../utils/dateUtil";
import AutomationService from "./automationService";
import PortfolioService from "./portfolioService";
import ProviderService from "./providerService";
import RewardService from "./rewardService";
import { SAVINGS_PRODUCT_MINIMUM_WK_ORDER_AMOUNT, TransactionService } from "./transactionService";
import UserDataRequestService from "./userDataRequestService";
import UserService from "./userService";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;
const DAYS_DIFF_DUPLICATE_REPEATING_TOP_UPS = 20;
const MAILCHIMP_VALIDATION_PERIOD_DAYS = 365;
const MAILCHIMP_VALIDATION_BATCH_SIZE = 5;
const MAILCHIMP_STATUSES_TO_NOT_VALIDATE = ["Verification Failed"];
const MAILCHIMP_REFERRED_BY_EMAIL_SUFFIXES_TO_NOT_VALIDATE = [".<EMAIL>", ".<EMAIL>"];
const MINIMUM_WEALTHKERNEL_PORTFOLIO_BALANCE = 500;
const MISMATCH_PRE_REQUISITES_CACHE_EXPIRATION = 60 * 60 * 8; // 8 hours
const CHECK_MODEL_STATE_BATCH_SIZE = 5;

class DataVerificationService {
  private static _isinToAssetCommonIdMap = Object.fromEntries(
    Object.entries(ASSET_CONFIG).map(([key, config]) => [config.isin, key as investmentUniverseConfig.AssetType])
  );
  private static _isinToSavingsProductIdMap = Object.fromEntries(
    Object.entries(SAVINGS_PRODUCT_CONFIG_GLOBAL).map(([key, config]) => [
      config.isin,
      key as savingsUniverseConfig.SavingsProductType
    ])
  );

  /**
   * ===============
   * PUBLIC METHODS
   * ===============
   */

  /**
   * @description Validates that db stored values of cash and holdings for real portfolios, are matching the ones
   * on Wealthkernel API.
   *
   * Checks are run on portfolios that have the following characteristics:
   * - a wealthkernel portfolio id exists, which means that a corresponding wealthkernel portfolio has been created
   *
   * The steps are:
   * 1. Retrieve valuation
   * 2. Skip process if valuation does not exist
   * 3. Get all transactions - both the ones needed for the gross valuation calc as well as the pending ones
   * 4. Skip check if pending transactions exist
   * 5. Skip check if user has suspended account or failed kyc
   * 6. Compare cash
   * 7. Compare holdings
   *
   */
  public static async findCashHoldingsMismatches(wealthkernelService: WealthkernelService): Promise<void> {
    let allCashMatch = true;
    let allHoldingsMatch = true;

    await PortfolioService.getPortfoliosStreamed(
      { wealthkernelExists: true },
      { currentTicker: false, owner: true }
    ).eachAsync(
      async (portfolios) => {
        const portfolioPromises = portfolios.map(async (portfolio: PortfolioDocument) => {
          const { providers, cash, currency, holdings, savings } = portfolio;
          const user = portfolio.owner as UserDocument;

          try {
            // 1. skip process if owner has not converted portfolio or has failed KYC
            if (!user?.hasConvertedPortfolio || user?.hasFailedKyc) {
              return;
            }

            // 2. fetch balance & valuation
            const [balances, valuation] = await Promise.all([
              wealthkernelService.listCashBalances(providers?.wealthkernel.id),
              wealthkernelService.retrieveLatestValuationByPortfolio(providers?.wealthkernel.id)
            ]);
            const balance = balances?.[0];

            // 3. skip process if valuation or balance does not exist
            if (!balance || !valuation) {
              logger.warn(`Balance/valuation wasn't retrieved for ${providers?.wealthkernel.id}`, {
                module: "DataVerificationService",
                method: "findCashHoldingsMismatches",
                data: { wealthkernelPortfolioId: providers?.wealthkernel.id, balance, valuation }
              });
              return;
            }

            // 4. fetch all transactions - both the ones needed for the gross valuation calc as well as the pending ones
            const { hasActiveTransactions, aggregatedDbCash } =
              await DataVerificationService._getCashHoldingMismatchPrerequisites(portfolio);

            // 5. skip check if pending transactions exist
            if (hasActiveTransactions) {
              logger.info(
                `User ${user.id} has pending asset/rebalance transactions, therefore we won't try to find mismatches`,
                {
                  module: "DataVerificationService",
                  method: "findCashHoldingsMismatches",
                  data: { wealthkernelPortfolioId: providers?.wealthkernel.id }
                }
              );
              return;
            }

            // 6. compare cash
            const cashMatch = DataVerificationService._compareDbToWkCash(
              providers?.wealthkernel?.id,
              balance,
              cash[currency].available,
              aggregatedDbCash,
              user
            );
            if (!cashMatch) {
              allCashMatch = false;
            }
            const settledAvailableCashMatch = DataVerificationService._compareAvailableToSettledCash(
              cash[currency],
              providers?.wealthkernel?.id
            );
            if (!settledAvailableCashMatch) {
              allCashMatch = false;
            }

            // 7. compare holdings
            const investmentHoldingsMatch = DataVerificationService._compareDbToWkInvestmentHoldings(
              providers?.wealthkernel?.id,
              holdings,
              valuation
            );
            const savingsHoldingsMatch = DataVerificationService._compareDbToWkSavingsHoldings(
              providers?.wealthkernel?.id,
              savings,
              valuation
            );

            if (!investmentHoldingsMatch || !savingsHoldingsMatch) {
              allHoldingsMatch = false;
            }
          } catch (err) {
            logger.error(`Could not verify cash/holdings matching Wealthkernel for user ${user.id}`, {
              module: "DataVerificationService",
              method: "findCashHoldingsMismatches",
              data: {
                error: err
              }
            });
            captureException(err);
          }
        });

        await Promise.allSettled(portfolioPromises);
      },
      { batchSize: 2 }
    );

    if (allCashMatch) {
      logger.info("🥳 Cash in database matches wealthkernel data!", {
        module: "DataVerificationService",
        method: "findCashHoldingsMismatches"
      });
    }

    if (allHoldingsMatch) {
      logger.info("🥳 Holdings in database match wealthkernel data!", {
        module: "DataVerificationService",
        method: "findCashHoldingsMismatches"
      });
    }
  }

  public static async checkUsersWithMissingSummaries(): Promise<void> {
    const entriesForMissingSummaries = (await DailySummarySnapshot.aggregate([
      {
        $group: {
          _id: "$metadata.owner",
          lastSummaryDate: { $max: "$date" },
          summaryCount: { $sum: 1 }
        }
      },
      {
        $match: {
          summaryCount: { $gt: 0 },
          lastSummaryDate: {
            $lt: DateUtil.getStartOfDay(DateUtil.calculatePreviousWorkDay(new Date(Date.now())))
          }
        }
      },
      {
        $project: {
          userId: "$_id",
          lastSummaryDate: 1,
          _id: 0
        }
      }
    ])) as { lastSummaryDate: Date; userId: mongoose.Types.ObjectId }[];

    const activeEntriesForMissingSummaries = await Promise.all(
      entriesForMissingSummaries.map(async (entry) => {
        const user = await UserService.getUser(entry.userId.toString());

        if (user && !user.isDeleted) {
          return {
            userId: entry.userId.toString(),
            lastSummaryDate: entry.lastSummaryDate
          };
        }
      })
    );

    // Filter out null entries (deleted users)
    const filteredEntries = activeEntriesForMissingSummaries.filter((entry) => !!entry);

    if (filteredEntries.length > 0) {
      logger.error(`${filteredEntries.length} missing summaries found!`, {
        module: "DataVerificationService",
        method: "checkUsersWithMissingSummaries",
        data: {
          entriesForMissingSummaries: filteredEntries
        }
      });
    } else {
      logger.info("🥳 No missing summaries found! 🥳!", {
        module: "DataVerificationService",
        method: "checkUsersWithMissingSummaries"
      });
    }
  }

  public static async findDuplicateRepeatingTopUps(): Promise<void> {
    const automations = await Automation.find({
      active: true,
      category: { $in: ["SavingsTopUpAutomation", "TopUpAutomation"] }
    }).populate("owner");

    let duplicateTopUpsFound = false;
    for (let i = 0; i < automations.length; i++) {
      const automation = automations[i];
      const owner = automation.owner as UserDocument;

      const recentDeposits = await DepositCashTransaction.find({
        linkedAutomation: automation._id,
        createdAt: { $gte: DateUtil.getDateOfDaysAgo(new Date(), 30) }
      }).sort({ createdAt: -1 });

      if (recentDeposits.length <= 1) continue;

      const latestDeposit = recentDeposits[0];
      const secondLatestDeposit = recentDeposits[1];
      const daysDiffBetweenDeposits = Math.abs(
        DateUtil.dateDiffInExactDays(new Date(latestDeposit.createdAt), new Date(secondLatestDeposit.createdAt))
      );

      if (daysDiffBetweenDeposits <= DAYS_DIFF_DUPLICATE_REPEATING_TOP_UPS) {
        duplicateTopUpsFound = true;
        logger.error(
          `Potentially duplicate ${automation.category} top-up for user ${owner.email} - ${daysDiffBetweenDeposits} days close`,
          {
            module: "DataVerificationService",
            method: "checkDuplicateRepeatingTopUps"
          }
        );
      }
    }

    if (!duplicateTopUpsFound) {
      logger.info("🥳 No duplicate repeating top-ups found!", {
        module: "DataVerificationService",
        method: "checkDuplicateRepeatingTopUps"
      });
    }
  }

  /**
   * @description Validates that member state in Mailchimp matches user state in our DB.
   * Specifically, we check for the fields referred, referredBy, and status.
   * We compare the users in our DB with the users in Mailchimp, and not the other way around,
   * because we know there are users in Mailchimp who have not signed up and therefore
   * do not exist in our DB.
   *
   * We do not validate users who are either ADMIN or have been created before MAILCHIMP_VALIDATION_PERIOD_DAYS days ago
   *
   * Method is used by nightly cron task runner.
   */
  public static async checkMailchimpState(): Promise<void> {
    await UserService.getUsersStreamed(
      {
        createdAfter: DateUtil.getDateOfDaysAgo(new Date(), MAILCHIMP_VALIDATION_PERIOD_DAYS)
      },
      "userDataRequests"
    ).eachAsync(
      async (users) => {
        const validationTasks = users
          .filter(
            (user: UserDocument) => !user.role.includes(UserTypeEnum.ADMIN) && !user.hasDisassociationRequest
          )
          .map((user) => DataVerificationService._validateMailchimpUser(user));

        await Promise.allSettled(validationTasks);
      },
      { batchSize: MAILCHIMP_VALIDATION_BATCH_SIZE }
    );
  }

  /**
   * @description Validates that there no users with settled investments who have a
   * portfolioConversionStatus different than 'completed'.
   *
   * Method is used by nightly cron task runner.
   */
  public static async checkNonConvertedUsers(): Promise<void> {
    const convertedUserIDs = await UserService.getConvertedUserIds();
    const ownersOfSettledAssetTransactionsAndRewardsIDs =
      await UserService.getOwnersOfSettledTransactionsAndRewards();

    if (convertedUserIDs.length == ownersOfSettledAssetTransactionsAndRewardsIDs.length) {
      logger.info("🎉 DB state for converted users is valid!", {
        module: "DataVerificationService",
        method: "checkNonConvertedUsers"
      });
      return;
    } else {
      const convertedUsersWithoutSettledInvestments = convertedUserIDs.filter(
        (id) => !ownersOfSettledAssetTransactionsAndRewardsIDs.includes(id)
      );
      const nonConvertedOwnersOfSettledInvestments = ownersOfSettledAssetTransactionsAndRewardsIDs.filter(
        (id) => !convertedUserIDs.includes(id)
      );

      logger.error("❌ DB state for converted users is invalid!", {
        module: "DataVerificationService",
        method: "checkNonConvertedUsers",
        data: {
          convertedUsersWithoutSettledInvestments,
          nonConvertedOwnersOfSettledInvestments
        }
      });
    }
  }

  /**
   * @description Validates every KYC passed user has one portfolio (real),
   * one address and one account in our model.
   *
   * Method is used by nightly cron task runner.
   */
  public static async checkModelState(): Promise<void> {
    let errorFound = false;

    await UserService.getUsersStreamed({ kycStatus: KycStatusEnum.PASSED }, [
      "portfolios",
      "addresses",
      "accounts"
    ]).eachAsync(
      async (users) => {
        await Promise.all(
          users.map(async (user) => {
            if (user.portfolios.length !== 1) {
              errorFound = true;
              logger.error(`User ${user.email} has ${user.portfolios.length} portfolios`, {
                module: "DataVerificationService",
                method: "checkModelState",
                userEmail: user.email
              });
            }

            // Check address
            if (user.addresses.length !== 1) {
              errorFound = true;
              logger.error(`User ${user.email} has ${user.addresses.length} addresses`, {
                module: "DataVerificationService",
                method: "checkModelState",
                userEmail: user.email
              });
            }

            // Check account
            if (user.accounts.length !== 1) {
              errorFound = true;
              logger.error(`User ${user.email} has ${user.accounts.length} accounts`, {
                module: "DataVerificationService",
                method: "checkModelState",
                userEmail: user.email
              });
            }
          })
        );
      },
      { batchSize: CHECK_MODEL_STATE_BATCH_SIZE }
    );

    if (!errorFound) {
      logger.info("🥳 Completed user model state data verification check, no errors found!", {
        module: "DataVerificationService",
        method: "checkModelState"
      });
    }
  }

  /**
   * @description Logs any transactions that haven't been settled in the appropriate period.
   *
   * Method is used by nightly cron task runner.
   */
  public static async checkStagnantTransactions(): Promise<void> {
    const today = new Date(Date.now());

    const daysToConsiderAssetTransactionStagnant = 2;
    const stagnantAssetTransactions = (await AssetTransaction.find({
      status: { $in: ["Pending", "PendingDeposit", "PendingGift"] },
      linkedAutomation: { $exists: false },
      createdAt: { $lte: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderAssetTransactionStagnant) }
    })) as TransactionDocument[];

    const daysToConsiderWealthyhoodDividendTransactionStagnant = 1;
    const wealthyhoodDividendTransactions = (await TransactionService.getTransactions(
      {
        categories: ["WealthyhoodDividendTransaction"],
        statuses: ["Pending"],
        creationDate: {
          endDate: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderWealthyhoodDividendTransactionStagnant)
        }
      },
      null
    )) as TransactionDocument[];

    // We consider direct debit deposits stagnant if they fall under one of two categories:
    // 1. They have NOT been submitted to WK or GoCardless even though they were created more than 1 work day ago.
    // 2. They have been submitted to WK but are still pending even though they should be settled based on our direct debit config.
    // 3. They have been submitted to GoCardless but are still pending even though they should be settled based on our direct debit config.

    // First case: unsubmitted direct debit deposits
    const daysToConsiderUnsubmittedDirectDebitDepositTransactionStagnant = 1;
    const unsubmittedDirectDebitDepositTransactions = await DepositCashTransaction.find({
      status: "Pending",
      createdAt: {
        $lte: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderUnsubmittedDirectDebitDepositTransactionStagnant)
      },
      linkedAutomation: { $exists: true },
      "directDebit.providers.wealthkernel.id": { $exists: false },
      "directDebit.providers.gocardless.id": { $exists: false }
    });

    // Second case: submitted direct debit deposits
    const daysToConsiderSubmittedDirectDebitDepositTransactionStagnant =
      WORK_DAYS_TO_CREATE_DEPOSITS_BEFORE_AUTOMATION_DATE[entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK] +
      ESTIMATED_WORK_DAYS_TO_RECEIVE_FUNDS_AFTER_COLLECTION[ProviderEnum.WEALTHKERNEL];
    const submittedDirectDebitDepositTransactions = (await TransactionService.getTransactions(
      {
        categories: ["DepositCashTransaction"],
        statuses: ["Pending"],
        hasLinkedAutomation: true,
        hasDirectDebitWealthkernelId: true,
        creationDate: {
          endDate: DateUtil.getDateNWorkDaysAgo(
            today,
            daysToConsiderSubmittedDirectDebitDepositTransactionStagnant
          )
        }
      },
      null
    )) as TransactionDocument[];

    // Third case: submitted direct debit deposits with intermediary
    const daysToConsiderSubmittedDirectDebitWithIntermediaryDepositTransactionStagnant =
      WORK_DAYS_TO_CREATE_DEPOSITS_BEFORE_AUTOMATION_DATE[entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE] +
      ESTIMATED_WORK_DAYS_TO_RECEIVE_FUNDS_AFTER_COLLECTION[ProviderEnum.GOCARDLESS];
    const submittedDirectDebitWithIntermediaryDepositTransactions = (await TransactionService.getTransactions(
      {
        categories: ["DepositCashTransaction"],
        statuses: ["Pending"],
        hasLinkedAutomation: true,
        hasDirectDebitGoCardlessId: true,
        creationDate: {
          endDate: DateUtil.getDateNWorkDaysAgo(
            today,
            daysToConsiderSubmittedDirectDebitWithIntermediaryDepositTransactionStagnant
          )
        }
      },
      null
    )) as TransactionDocument[];

    // We consider a direct debit asset transaction stagnant if it has exceeded the relevant deposit limit
    // plus one work day to allow for orders to be matched.
    const daysToConsiderDirectDebitAssetTransactionStagnant =
      daysToConsiderSubmittedDirectDebitDepositTransactionStagnant + 1;
    const directDebitAssetTransactions = (await TransactionService.getTransactions(
      {
        categories: ["AssetTransaction"],
        statuses: ["Pending"],
        hasLinkedAutomation: true,
        creationDate: {
          endDate: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderDirectDebitAssetTransactionStagnant)
        }
      },
      null
    )) as TransactionDocument[];

    const minimumDaysToConsiderCashbackTransactionStagnant = 1;
    const cashbackTransactions = (await TransactionService.getTransactions(
      {
        categories: ["CashbackTransaction"],
        statuses: ["Pending"],
        creationDate: {
          endDate: DateUtil.getDateNWorkDaysAgo(today, minimumDaysToConsiderCashbackTransactionStagnant)
        }
      },
      null,
      {
        linkedAssetTransaction: true
      }
    )) as CashbackTransactionDocument[];
    const filteredCashbackTransactions = cashbackTransactions.filter((cashback: CashbackTransactionDocument) => {
      /**
       * If cashback is linked to a repeating investment, validate with a different threshold
       */
      if ((cashback.linkedAssetTransaction as AssetTransactionDocument).linkedAutomation) {
        return (
          cashback.createdAt <
          DateUtil.getDateNWorkDaysAgo(today, daysToConsiderDirectDebitAssetTransactionStagnant)
        );
      }

      return true;
    });

    const hoursToConsiderOpenBankingDepositTransactionStagnant = 2; //hours
    const openBankingDepositTransactions = (await TransactionService.getTransactions(
      {
        categories: ["DepositCashTransaction"],
        statuses: ["Pending"],
        hasLinkedAutomation: false,
        creationDate: {
          endDate: DateUtil.getDateOfHoursAgo(today, hoursToConsiderOpenBankingDepositTransactionStagnant)
        },
        depositMethod: DepositMethodEnum.OPEN_BANKING
      },
      null
    )) as TransactionDocument[];

    const daysToConsiderBankTransferDepositTransactionStagnant = 2; //days
    const bankTransferDepositTransactions = (await TransactionService.getTransactions(
      {
        categories: ["DepositCashTransaction"],
        statuses: ["Pending"],
        hasLinkedAutomation: false,
        creationDate: {
          endDate: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderBankTransferDepositTransactionStagnant)
        },
        depositMethod: DepositMethodEnum.BANK_TRANSFER
      },
      null
    )) as TransactionDocument[];

    const daysToConsiderWithdrawalTransactionStagnant = 3;
    const withdrawalTransactions = (await WithdrawalCashTransaction.find({
      $or: [
        { "providers.wealthkernel.id": { $exists: false } },
        { "providers.wealthkernel.status": "Pending" },
        {
          withdrawalMethod: WithdrawalMethodEnum.WITH_INTERMEDIARY,
          "transferWithIntermediary.collection.outgoingPayment.providers.devengo.status": { $ne: "confirmed" }
        }
      ],
      createdAt: { $lte: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderWithdrawalTransactionStagnant) }
    })) as TransactionDocument[];

    const daysToConsiderRebalanceTransactionStagnant = 5;
    const rebalanceTransactions = (await TransactionService.getTransactions(
      {
        categories: ["RebalanceTransaction"],
        statuses: ["Pending"],
        creationDate: { endDate: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderRebalanceTransactionStagnant) }
      },
      null
    )) as TransactionDocument[];

    const daysToConsiderCardChargeTransactionStagnant = 9;
    const cardChargeTransactions = (await TransactionService.getChargeTransactions({
      statuses: ["Pending"],
      chargeMethods: ["card"],
      creationDate: {
        endDate: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderCardChargeTransactionStagnant)
      }
    })) as TransactionDocument[];

    // We consider charge transactions stagnant if they fall under one of two categories:
    // 1. They have NOT been submitted to WK even though they were created before in a previous month
    // 2. They have been submitted to WK at least 2 days ago but are still pending
    const unsubmittedChargeTransactions = (await TransactionService.getChargeTransactions({
      statuses: ["Pending"],
      chargeMethods: ["cash", "combined", "holdings", "orders"],
      creationDate: { endDate: DateUtil.getFirstDayOfThisMonth() },
      hasWealthkernelId: false
    })) as TransactionDocument[];

    const daysSubmittedToConsiderChargeTransactionStagnant = 1;
    const submittedChargeTransactions = (await TransactionService.getChargeTransactions({
      statuses: ["Pending"],
      chargeMethods: ["cash", "combined", "holdings", "orders"],
      hasWealthkernelId: true,
      wealthkernelSubmissionDate: {
        endDate: DateUtil.getDateOfDaysAgo(new Date(Date.now()), daysSubmittedToConsiderChargeTransactionStagnant)
      }
    })) as TransactionDocument[];

    const daysToConsiderSavingsTopupTransactionsStagnant = 2;
    const stagnantSavingsTopupsTransactions = (await SavingsTopupTransaction.find(
      {
        status: "Pending",
        createdAt: {
          $lte: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderSavingsTopupTransactionsStagnant)
        }
      },
      null
    )) as TransactionDocument[];

    const daysToConsiderSavingsWithdrawalTransactionsStagnant = 3;
    const stagnantSavingsWithdrawalsTransactions = (await SavingsWithdrawalTransaction.find(
      {
        status: { $in: ["Pending", "PendingTopUp"] },
        createdAt: {
          $lte: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderSavingsWithdrawalTransactionsStagnant)
        }
      },
      null
    )) as TransactionDocument[];

    const daysToConsiderSavingsDividendTransactionsStagnant = 2;
    const stagnantSavingsDividendTransactions = (await SavingsDividendTransaction.find(
      {
        status: "Pending",
        createdAt: {
          $lte: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderSavingsDividendTransactionsStagnant)
        }
      },
      null
    )) as TransactionDocument[];

    const allTransactionsPromises = stagnantAssetTransactions
      .concat(openBankingDepositTransactions)
      .concat(bankTransferDepositTransactions)
      .concat(withdrawalTransactions)
      .concat(filteredCashbackTransactions)
      .concat(wealthyhoodDividendTransactions)
      .concat(rebalanceTransactions)
      .concat(directDebitAssetTransactions)
      .concat(submittedDirectDebitDepositTransactions)
      .concat(submittedDirectDebitWithIntermediaryDepositTransactions)
      .concat(unsubmittedDirectDebitDepositTransactions)
      .concat(unsubmittedChargeTransactions)
      .concat(submittedChargeTransactions)
      .concat(cardChargeTransactions)
      .concat(stagnantSavingsTopupsTransactions)
      .concat(stagnantSavingsWithdrawalsTransactions)
      .concat(stagnantSavingsDividendTransactions)
      .map(async (transaction) => {
        await transaction.populate("owner");

        return {
          id: transaction.id,
          email: (transaction.owner as UserDocument).email,
          owner: transaction.owner.id.toString(),
          createdAt: transaction.createdAt,
          category: transaction.category,
          consideration: transaction.consideration
        };
      });

    const allTransactionsSettled = await Promise.allSettled(allTransactionsPromises);

    // Log errors for rejected promises
    allTransactionsSettled
      .filter((transactionPromise) => transactionPromise.status === "rejected")
      .forEach((transactionPromise: PromiseRejectedResult) => {
        logger.error(`Transaction failed to process with reason: ${transactionPromise.reason}`, {
          module: "DataVerificationService",
          method: "checkStagnantTransactions"
        });
      });

    const allTransactions = allTransactionsSettled
      .filter((transactionPromise) => transactionPromise.status === "fulfilled")
      .map((transactionPromise) => (transactionPromise as PromiseFulfilledResult<any>).value);

    if (allTransactions.length > 0) {
      logger.error(`There are ${allTransactions.length} transactions that are stagnant!`, {
        module: "DataVerificationService",
        method: "checkStagnantTransactions",
        data: {
          allTransactions
        }
      });
    } else {
      logger.info("🥳 There are no stagnant transactions!", {
        module: "DataVerificationService",
        method: "checkStagnantTransactions"
      });
    }
  }

  public static async checkStagnantCreditTickets(): Promise<void> {
    const today = new Date(Date.now());

    const hoursToConsiderCreditTicketStagnant = 2;
    const creditTickets = await CreditTicket.find({
      status: "Pending",
      createdAt: { $lte: DateUtil.getDateOfHoursAgo(today, hoursToConsiderCreditTicketStagnant) }
    });

    const stagnantCreditTickets = await Promise.all(
      creditTickets.map(async (creditTicket) => {
        const deposit = await DepositCashTransaction.findOne({
          linkedCreditTicket: creditTicket.id
        });

        return {
          id: creditTicket.id,
          owner: creditTicket.owner.toString(),
          createdAt: creditTicket.createdAt,
          depositId: deposit?.id
        };
      })
    );

    if (stagnantCreditTickets.length > 0) {
      logger.error(`There are ${stagnantCreditTickets.length} credit tickets that are stagnant!`, {
        module: "DataVerificationService",
        method: "checkStagnantCreditTickets",
        data: {
          creditTickets: stagnantCreditTickets.map((creditTicket) => {
            return {
              id: creditTicket.id,
              owner: creditTicket.owner,
              createdAt: creditTicket.createdAt,
              depositId: creditTicket.depositId
            };
          })
        }
      });
    } else {
      logger.info("🥳 There are no stagnant credit tickets!", {
        module: "DataVerificationService",
        method: "checkStagnantCreditTickets"
      });
    }
  }

  public static async checkStagnantPayouts(): Promise<void> {
    const today = new Date(Date.now());

    const daysToConsiderPayoutsStagnant = 1;
    const stagnantPayouts = await Payout.find({
      status: "Pending",
      createdAt: { $lte: DateUtil.getDateOfDaysAgo(today, daysToConsiderPayoutsStagnant) }
    });

    if (stagnantPayouts.length > 0) {
      logger.error(`There are ${stagnantPayouts.length} payouts that are stagnant!`, {
        module: "DataVerificationService",
        method: "checkStagnantPayouts",
        data: {
          payouts: stagnantPayouts.map((payout) => {
            return {
              id: payout.id
            };
          })
        }
      });
    } else {
      logger.info("🥳 There are no stagnant payouts!", {
        module: "DataVerificationService",
        method: "checkStagnantPayouts"
      });
    }
  }

  /**
   * @description Logs any repeating investments/savings that are active, have day of month set to at most 5 work days
   * into the future, but still do NOT have a deposit & asset/savings transaction created (when looking back 5 more days).
   *
   * Method is used by nightly cron task runner.
   */
  public static async checkStagnantAutomations(): Promise<void> {
    const today = new Date(Date.now());
    const daysToConsiderAutomationStagnant = 5;

    const daysOfMonthToCheck = DateUtil.getAllDatesBetweenTwoDates(
      today,
      DateUtil.getDateAfterNthUKWorkDays(today, daysToConsiderAutomationStagnant)
    ).map((date) => DateUtil.convertIntoRecurrenceDate(date));

    const automations = (
      await AutomationService.getAutomations(
        {
          categories: ["TopUpAutomation", "SavingsTopUpAutomation"],
          activeOnly: true,
          daysOfMonth: daysOfMonthToCheck,
          isInitialised: true
        },
        { mandate: false, owner: true }
      )
    ).data;

    const stagnantAutomations = await Promise.all(
      automations.map(async (automation) => {
        const owner = automation.owner as UserDocument;

        // We create repeating investments/savings based on WORK_DAYS_TO_CREATE_DEPOSITS_BEFORE_AUTOMATION_DATE,
        // and we add one day to that to make sure we catch all transactions regardless of time created.
        const daysToLookBackForTransactions =
          WORK_DAYS_TO_CREATE_DEPOSITS_BEFORE_AUTOMATION_DATE[owner.companyEntity] + 1;

        if (automation.category === "TopUpAutomation") {
          const [assetTransaction, deposit] = await Promise.all([
            AssetTransaction.findOne({
              linkedAutomation: automation.id,
              createdAt: { $gte: DateUtil.getDateNWorkDaysAgo(today, daysToLookBackForTransactions) }
            }),
            DepositCashTransaction.findOne({
              linkedAutomation: automation.id,
              createdAt: { $gte: DateUtil.getDateNWorkDaysAgo(today, daysToLookBackForTransactions) }
            })
          ]);

          if (!assetTransaction || !deposit) {
            return automation;
          }
        } else if (automation.category === "SavingsTopUpAutomation") {
          const [savingsTopUp, deposit] = await Promise.all([
            SavingsTopupTransaction.findOne({
              linkedAutomation: automation.id,
              createdAt: { $gte: DateUtil.getDateNWorkDaysAgo(today, daysToLookBackForTransactions) }
            }),
            DepositCashTransaction.findOne({
              linkedAutomation: automation.id,
              createdAt: { $gte: DateUtil.getDateNWorkDaysAgo(today, daysToLookBackForTransactions) }
            })
          ]);

          if (!savingsTopUp || !deposit) {
            return automation;
          }
        }
      })
    ).then((automations) => automations.filter((automation) => !!automation));

    if (stagnantAutomations.length > 0) {
      logger.error(`There are ${stagnantAutomations.length} automations that are stagnant!`, {
        module: "DataVerificationService",
        method: "checkStagnantAutomations",
        data: {
          automations: stagnantAutomations.map((automation) => {
            const owner = automation.owner as UserDocument;

            return {
              id: automation.id,
              owner: owner.id,
              email: owner.email
            };
          })
        }
      });
    } else {
      logger.info("🥳 There are no stagnant automations!", {
        module: "DataVerificationService",
        method: "checkStagnantAutomations"
      });
    }
  }

  /**
   * @description Logs any user data requests that haven't been completed for more than 35 days.
   *
   * Method is used by nightly cron task runner.
   */
  public static async checkStagnantUserDataRequests(): Promise<void> {
    const today = new Date(Date.now());

    const daysToConsiderUserDataRequestStagnant = 35;
    const stagnantUserDataRequests = (await UserDataRequestService.getUserDataRequests({
      statuses: ["Created"],
      creationDate: { endDate: DateUtil.getDateOfDaysAgo(today, daysToConsiderUserDataRequestStagnant) }
    })) as UserDataRequestDocument[];

    if (stagnantUserDataRequests.length > 0) {
      logger.error(`There are ${stagnantUserDataRequests.length} user data requests that are stagnant!`, {
        module: "DataVerificationService",
        method: "checkStagnantUserDataRequests",
        data: {
          stagnantUserDataRequests: stagnantUserDataRequests.map((request) => {
            return {
              id: request.id,
              owner: request.owner,
              requestType: request.requestType
            };
          })
        }
      });
    } else {
      logger.info("🥳 There are no stagnant user data requests!", {
        module: "DataVerificationService",
        method: "checkStagnantUserDataRequests"
      });
    }
  }

  /**
   * @description Logs any rewards that haven't been completed for more than 2 work days.
   *
   * Method is used by nightly cron task runner.
   */
  public static async checkStagnantRewards(): Promise<void> {
    const today = new Date(Date.now());

    const daysToConsiderRewardsStagnant = 2;
    const stagnantRewards = (
      (await RewardService.getRewards({
        status: "Pending",
        accepted: true,
        creationDate: { endDate: DateUtil.getDateNWorkDaysAgo(today, daysToConsiderRewardsStagnant) }
      })) as { data: RewardDocument[] }
    ).data;

    if (stagnantRewards.length > 0) {
      logger.error(`There are ${stagnantRewards.length} rewards that are stagnant!`, {
        module: "DataVerificationService",
        method: "checkStagnantRewards",
        data: {
          rewards: stagnantRewards.map((reward) => {
            return {
              id: reward.id,
              owner: reward.targetUser
            };
          })
        }
      });
    } else {
      logger.info("🥳 There are no stagnant rewards!", {
        module: "DataVerificationService",
        method: "checkStagnantRewards"
      });
    }
  }

  /**
   * @description Checks that our Wealthyhood bonus portfolio balances (both UK & EU) are more than MINIMUM_WEALTHKERNEL_PORTFOLIO_BALANCE.
   */
  public static async checkWealthyhoodCashBalance(): Promise<void> {
    const [ukBonusesCashBalances, euBonusesCashBalances] = await Promise.all([
      ProviderService.getBrokerageService(entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK).listCashBalances(
        process.env.WEALTHKERNEL_WH_PORTFOLIO_ID_BONUS_UK
      ),
      ProviderService.getBrokerageService(entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE).listCashBalances(
        process.env.WEALTHKERNEL_WH_PORTFOLIO_ID_BONUS_EU
      )
    ]);

    if (new Decimal(ukBonusesCashBalances[0].value.amount).lessThan(MINIMUM_WEALTHKERNEL_PORTFOLIO_BALANCE)) {
      logger.error(
        `💸 Our UK bonus cash balance is currently ${ukBonusesCashBalances[0].value.amount}, we should top-up ASAP...`,
        {
          module: "DataVerificationService",
          method: "checkWealthyhoodCashBalance"
        }
      );
    }

    if (new Decimal(euBonusesCashBalances[0].value.amount).lessThan(MINIMUM_WEALTHKERNEL_PORTFOLIO_BALANCE)) {
      logger.error(
        `💸 Our EU bonus cash balance is currently ${euBonusesCashBalances[0].value.amount}, we should top-up ASAP...`,
        {
          module: "DataVerificationService",
          method: "checkWealthyhoodCashBalance"
        }
      );
    }
  }

  /**
   * @description
   * Check active subscriptions with nextChargeAt pointing at the past
   */
  public static async checkSubscriptionNextChargeDate(): Promise<void> {
    const subscriptionsWithInvalidNextChargeDate = await Subscription.find({
      active: true,
      nextChargeAt: { $lt: new Date(Date.now()) }
    });

    subscriptionsWithInvalidNextChargeDate.forEach((subscription) => {
      logger.error(`Found subscription ${subscription.id} with old nextChargeAt field`, {
        module: "DataVerificationService",
        method: "checkSubscriptionNextChargeDate",
        data: { subscription: subscription.id }
      });
    });

    if (subscriptionsWithInvalidNextChargeDate.length > 0) {
      logger.error(
        `⏱️ Found ${subscriptionsWithInvalidNextChargeDate.length} subscriptions with invalid nextChargeAt field`,
        {
          module: "DataVerificationService",
          method: "checkSubscriptionNextChargeDate"
        }
      );
    } else {
      logger.info("🥳 No subscriptions have invalid nextChargeAt field!", {
        module: "DataVerificationService",
        method: "checkSubscriptionNextChargeDate"
      });
    }
  }

  /**
   * @description Logs any bank accounts that are in pending state for more than they should.
   *
   */
  public static async checkStagnantBankAccounts(): Promise<void> {
    const today = new Date(Date.now());
    const hoursToConsiderPendingBankAccountsStagnant = 12; //hours
    const bankAccounts = await BankAccount.find(
      {
        $or: [{ "providers.wealthkernel.status": "Pending" }, { "providers.wealthyhood.status": "Pending" }],
        createdAt: { $lt: DateUtil.getDateOfHoursAgo(today, hoursToConsiderPendingBankAccountsStagnant) },
        active: true
      },
      null
    );
    const bankAccountsData = bankAccounts.map((bankAccount) => ({
      id: bankAccount.id,
      owner: bankAccount.owner,
      createdAt: bankAccount.createdAt
    }));

    if (bankAccountsData.length > 0) {
      logger.error(`There are ${bankAccountsData.length} bank accounts that are stagnant!`, {
        module: "DataVerificationService",
        method: "checkStagnantBankAccounts",
        data: {
          allBankAccounts: bankAccountsData
        }
      });
    } else {
      logger.info("🥳 There are no stagnant bank accounts!", {
        module: "DataVerificationService",
        method: "checkStagnantBankAccounts"
      });
    }
  }

  /**
   * @description Checks that the Content Entries that should have been published
   * have contentful.id
   */
  public static async checkUnpublishedFinimizeContentEntries(): Promise<void> {
    const contentEntries = await ContentEntry.find({
      "providers.finimize.id": { $exists: true },
      "providers.contentful.id": { $exists: false },
      publishAt: {
        $lt: DateUtil.getDateOfDaysAgo(new Date(), 2)
      }
    });

    if (contentEntries.length > 0) {
      logger.error(`There are ${contentEntries.length} unpublished finimize content entries!`, {
        module: "DataVerificationService",
        method: "checkUnpublishedFinimizeContentEntries",
        data: {
          allContentEntries: contentEntries
        }
      });
    } else {
      logger.info("🥳 There are no unpublished finimize content entries!", {
        module: "DataVerificationService",
        method: "checkUnpublishedFinimizeContentEntries"
      });
    }
  }

  /**
   * ===============
   * PRIVATE METHODS
   * ===============
   */

  /**
   * @description Validates the state of a given Mailchimp user
   * @param user
   */
  private static async _validateMailchimpUser(user: UserDocument): Promise<void> {
    const fieldsToValidate: MailchimpUserMergeFieldType[] = [
      "REFERRED",
      "REFERREDBY",
      "STATUS",
      "REPINVESTM",
      "AUTREBALAN"
    ];

    const response = await MailchimpService.getMergeFieldsAndStatusForUserEmail(user.email, fieldsToValidate);

    const fields = response.mergeFields;
    const status = response.status;
    if (status !== "subscribed") {
      return;
    }
    await user.populate("addresses bankAccounts portfolios");

    if (
      !MAILCHIMP_REFERRED_BY_EMAIL_SUFFIXES_TO_NOT_VALIDATE.some((emailSuffix) =>
        fields.REFERREDBY.toLowerCase().endsWith(emailSuffix)
      )
    ) {
      const mailchimpIsReferred = fields.REFERRED && fields.REFERRED !== "False";
      // Validate Mailchimp referral state
      if ((fields.REFERREDBY && !mailchimpIsReferred) || (user.referredByEmail || "") !== fields.REFERREDBY) {
        logger.error(`Referral state in Mailchimp is invalid or does not match DB state for user ${user.email}`, {
          module: "DataVerificationService",
          method: "checkMailchimpState",
          data: {
            email: user.email,
            referred: fields.REFERRED,
            referredBy: fields.REFERREDBY,
            status: fields.STATUS,
            referredByEmail: user.referredByEmail
          }
        });
      }
    }

    const rebalanceAutomation: AutomationDocument = await RebalanceAutomation.findOne({
      owner: user.id,
      active: true
    });
    if (rebalanceAutomation && fields.AUTREBALAN !== "True") {
      logger.error(`Rebalance automation state in Mailchimp does not match DB state for user ${user.email}`, {
        module: "DataVerificationService",
        method: "checkMailchimpState",
        data: {
          email: user.email,
          rebalanceAutomation: fields.AUTREBALAN
        }
      });
    }

    const topUpAutomation: AutomationDocument = await TopUpAutomation.findOne({
      owner: user.id,
      active: true
    });
    if (topUpAutomation && fields.REPINVESTM !== "True") {
      logger.error(`Top-up automation state in Mailchimp does not match DB state for user ${user.email}`, {
        module: "DataVerificationService",
        method: "checkMailchimpState",
        data: {
          email: user.email,
          topUpAutomation: fields.REPINVESTM
        }
      });
    }

    if (MAILCHIMP_STATUSES_TO_NOT_VALIDATE.includes(fields.STATUS)) {
      return;
    }

    // Validate Mailchimp status against user state
    const transactions: TransactionDocument[] = await Transaction.find({
      owner: user.id,
      $or: [
        {
          category: "AssetTransaction",
          status: { $nin: ["PendingDeposit", "PendingGift", "Cancelled", "DepositFailed"] }
        },
        {
          category: "DepositCashTransaction",
          $or: [
            { "providers.wealthkernel.id": { $nin: [undefined, null, ""] } },
            { linkedAutomation: { $exists: true } }
          ]
        },
        {
          category: { $in: ["SavingsTopupTransaction", "SavingsWithdrawalTransaction"] },
          status: { $nin: ["PendingDeposit", "PendingTopUp", "DepositFailed"] }
        }
      ]
    });
    const rewardsWithOrders: RewardDocument[] = await Reward.find({
      targetUser: user.id,
      "order.providers.wealthkernel.id": { $nin: [undefined, null, ""] }
    });

    if (transactions.length > 0 || rewardsWithOrders.length > 0) {
      DataVerificationService._compareMailchimpToDbStatus(
        fields.STATUS,
        ["Deposit Created", "Deposit Succeeded", "Investment Created", "Investment Succeeded"],
        user.email
      );
    } else if (user.hasPassedKyc) {
      DataVerificationService._compareMailchimpToDbStatus(fields.STATUS, ["Verified"], user.email);
    } else if (user.taxResidencySubmitted) {
      DataVerificationService._compareMailchimpToDbStatus(
        fields.STATUS,
        ["Tax Details Submitted", "Personal Details Submitted"],
        user.email
      );
    } else if (user.addressSubmitted) {
      DataVerificationService._compareMailchimpToDbStatus(fields.STATUS, ["Address Submitted"], user.email);
    } else if (user.passportSubmitted) {
      DataVerificationService._compareMailchimpToDbStatus(
        fields.STATUS,
        ["Passport Details Submitted"],
        user.email
      );
    } else {
      DataVerificationService._compareMailchimpToDbStatus(fields.STATUS, ["Signed Up"], user.email);
    }
  }

  private static _compareAvailableToSettledCash(
    cash: { available: number; settled: number },
    wkPortfolioId: string
  ): boolean {
    const { available, settled } = cash;
    if (available !== settled) {
      logger.error(
        `Available cash (${available}) does not match settled cash (${settled}) for portfolio ${wkPortfolioId}`,
        {
          module: "DataVerificationService",
          method: "_compareAvailableToSettledCash"
        }
      );
    }
    return available === settled;
  }

  /**
   * @description Compares the value of the cash stored in database with the cash on wealthkernel
   * @param wealthkernelPortfolioId
   * @param balance
   * @param dbCash
   * @param aggregatedDbCash
   */
  private static _compareDbToWkCash(
    wealthkernelPortfolioId: string,
    balance: BalanceType,
    dbCash: number,
    aggregatedDbCash: number,
    user: UserDocument
  ): boolean {
    const wkCash = new Decimal(balance?.value?.amount ?? 0).toNumber();

    if (aggregatedDbCash !== wkCash) {
      logger.error(
        `Cash value mismatch for portfolio ${wealthkernelPortfolioId} | aggregated DB cash: ${aggregatedDbCash} | WK cash: ${wkCash}`,
        {
          module: "DataVerificationService",
          method: "_compareDbToWkCash",
          data: {
            wealthkernelPortfolioId: wealthkernelPortfolioId,
            dbCash: dbCash,
            aggregatedDbCash: aggregatedDbCash,
            wkCash: wkCash,
            owner: user.id,
            companyEntity: user.companyEntity
          }
        }
      );
    }

    return aggregatedDbCash === wkCash;
  }

  /**
   * @description Compares the holdings of a portfolio stored in database with the holdings on wealthkernel
   * Note: This method compare only the holdings that are part of the investment universe.
   * @returns boolean that indicates whether db document holdings are matching wealthkernel holdings
   * @param wealthkernelPortfolioId
   * @param dbHoldings
   * @param valuation
   */
  private static _compareDbToWkInvestmentHoldings(
    wealthkernelPortfolioId: string,
    dbHoldings: HoldingsType[],
    valuation: ValuationType
  ): boolean {
    const investmentValuationHoldings: HoldingValuationType[] = valuation.holdings.filter(
      ({ isin }) => !!DataVerificationService._isinToAssetCommonIdMap[isin]
    );

    const wkHoldingsDict = Object.fromEntries(
      investmentValuationHoldings
        .filter((holding) => holding.quantity > 0)
        .map((holding: HoldingValuationType) => [holding.isin, holding.quantity])
    );

    let holdingsMatch = true;

    if (dbHoldings.length > Object.values(wkHoldingsDict).length) {
      const dbHoldingsNotInWealthkernel = dbHoldings
        .filter(
          ({ assetCommonId }) =>
            !Object.keys(wkHoldingsDict)
              .map((isin) => isin)
              .some((isin) => isin === ASSET_CONFIG[assetCommonId].isin)
        )
        .map(({ assetCommonId }) => assetCommonId);

      logger.error(
        `Holdings length mismatch for portfolio ${wealthkernelPortfolioId} - DB holdings include holdings not in WK`,
        {
          module: "DataVerificationService",
          method: "_compareDbToWkInvestmentHoldings",
          data: { wealthkernelPortfolioId: wealthkernelPortfolioId, dbHoldingsNotInWealthkernel }
        }
      );
      holdingsMatch = false;
    } else if (dbHoldings.length < Object.values(wkHoldingsDict).length) {
      const wkHoldingsNotInDatabase = Object.keys(wkHoldingsDict).filter(
        (isin) => !dbHoldings.some(({ assetCommonId }) => isin === ASSET_CONFIG[assetCommonId].isin)
      );

      logger.error(
        `Holdings length mismatch for portfolio ${wealthkernelPortfolioId} - WK holdings include holdings not in DB`,
        {
          module: "DataVerificationService",
          method: "_compareDbToWkInvestmentHoldings",
          data: { wealthkernelPortfolioId: wealthkernelPortfolioId, wkHoldingsNotInDatabase }
        }
      );
      holdingsMatch = false;
    }

    dbHoldings.forEach(({ assetCommonId, quantity }) => {
      const isin = ASSET_CONFIG[assetCommonId].isin;
      if (wkHoldingsDict[isin] !== quantity) {
        holdingsMatch = false;
        logger.error(`Holdings mismatch for portfolio ${wealthkernelPortfolioId}`, {
          module: "DataVerificationService",
          method: "_compareDbToWkInvestmentHoldings",
          data: {
            wealthkernelPortfolioId: wealthkernelPortfolioId,
            asset: assetCommonId,
            wealthyhoodQuantity: quantity,
            wealthkernelQuantity: wkHoldingsDict[isin]
          }
        });
      }
    });

    return holdingsMatch;
  }

  /**
   * @description Compares the savings of a portfolio stored in database with the holdings on wealthkernel
   * Note: This method compare only the holdings that are part of the savings universe.
   * @returns boolean that indicates whether db document holdings are matching wealthkernel holdings
   * @param wealthkernelPortfolioId
   * @param dbSavings
   * @param valuation
   */
  private static _compareDbToWkSavingsHoldings(
    wealthkernelPortfolioId: string,
    dbSavings: Map<savingsUniverseConfig.SavingsProductType, SavingType>,
    valuation: ValuationType
  ): boolean {
    const savingsValuationHoldings: HoldingValuationType[] = valuation.holdings.filter(
      ({ isin }) => !!DataVerificationService._isinToSavingsProductIdMap[isin]
    );

    const wkHoldingsDict = Object.fromEntries(
      savingsValuationHoldings
        .filter((holding) => holding.quantity > 0)
        .map((holding: HoldingValuationType) => [holding.isin, holding.quantity])
    );

    let holdingsMatch = true;
    const dbSavingsProductIds = Array.from(dbSavings.keys());
    if (dbSavingsProductIds.length > Object.values(wkHoldingsDict).length) {
      const dbHoldingsNotInWealthkernel = dbSavingsProductIds.filter(
        (savingsProductId) =>
          !Object.keys(wkHoldingsDict)
            .map((isin) => isin)
            .some((isin) => isin === SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductId].isin)
      );

      logger.error(
        `Holdings length mismatch for portfolio ${wealthkernelPortfolioId} - DB holdings include holdings not in WK`,
        {
          module: "DataVerificationService",
          method: "_compareDbToWkSavingsHoldings",
          data: { wealthkernelPortfolioId: wealthkernelPortfolioId, dbHoldingsNotInWealthkernel }
        }
      );
      holdingsMatch = false;
    } else if (dbSavingsProductIds.length < Object.values(wkHoldingsDict).length) {
      const wkSavingsNotInDatabase = Object.keys(wkHoldingsDict).filter(
        (isin) =>
          !dbSavingsProductIds.some(
            (savingsProductId) => isin === SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductId].isin
          )
      );

      logger.error(
        `Holdings length mismatch for portfolio ${wealthkernelPortfolioId} - WK holdings include holdings not in DB`,
        {
          module: "DataVerificationService",
          method: "_compareDbToWkSavingsHoldings",
          data: {
            wealthkernelPortfolioId: wealthkernelPortfolioId,
            wkHoldingsNotInDatabase: wkSavingsNotInDatabase
          }
        }
      );
      holdingsMatch = false;
    }

    dbSavingsProductIds.forEach((savingsProductId) => {
      const isin = SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductId].isin;
      const dbQuantity = Decimal.div(dbSavings.get(savingsProductId)?.amount ?? 0, 100).toNumber();
      if (wkHoldingsDict[isin] !== dbQuantity) {
        holdingsMatch = false;
        logger.error(`Holdings mismatch for portfolio ${wealthkernelPortfolioId}`, {
          module: "DataVerificationService",
          method: "_compareDbToWkSavingsHoldings",
          data: {
            wealthkernelPortfolioId: wealthkernelPortfolioId,
            asset: savingsProductId,
            wealthyhoodQuantity: dbQuantity,
            wealthkernelQuantity: wkHoldingsDict[isin]
          }
        });
      }
    });

    return holdingsMatch;
  }

  private static _compareMailchimpToDbStatus(mailchimpStatus: string, dbStatuses: string[], email: string): void {
    if (!dbStatuses.includes(mailchimpStatus)) {
      logger.error(
        `Status in Mailchimp should be ${dbStatuses.join("/")} for ${email} but is ${mailchimpStatus}`,
        {
          module: "DataVerificationService",
          method: "_compareMailchimpToDbStatus",
          data: {
            email: email,
            expectedStatuses: dbStatuses,
            status: mailchimpStatus
          }
        }
      );
    }
  }

  /**
   * @description Aggregate the cash value of the portfolio that should match brokerage cash.
   *
   * Formula:
   * DB cash = Wealthkernel cash - withdrawals - rewards - pending charges
   * or
   * WK cash = DB cash + withdrawals + rewards + pending charges
   */
  private static _aggregateDbCashFromRelevantTransactions(
    dbCash: number,
    transactionsAffectingValuation: {
      pendingCharges: ChargeTransactionDocument[];
      rewards: RewardDocument[];
      pendingWithdrawals: WithdrawalCashTransactionDocument[];
      stagnantSavingsDividends: SavingsDividendTransactionDocument[];
    }
  ): number {
    const { pendingCharges, rewards, pendingWithdrawals, stagnantSavingsDividends } =
      transactionsAffectingValuation;

    const withdrawalAmount = pendingWithdrawals
      .map(({ consideration }) => new Decimal(consideration.amount))
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
      .div(100);

    const stagnantSavingsDividendAmount = stagnantSavingsDividends
      .map(({ consideration }) => new Decimal(consideration.amount))
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
      .div(100);

    const rewardedAmount = rewards
      .map(({ consideration, fees }) =>
        new Decimal(consideration.amount)
          .div(100)
          .plus(fees.fx.amount)
          .plus(fees.commission?.amount ?? 0)
          .plus(fees.executionSpread?.amount ?? 0)
      )
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0));

    const pendingChargeAmount = pendingCharges
      .map((transaction) => {
        const chargesHasOrders = ["holdings", "combined"].includes(transaction.chargeMethod);
        if (!chargesHasOrders) {
          return transaction.consideration.amount;
        } else {
          const holdingsAmountFromMatchedOrders = transaction.orders
            .filter((order) => order.providers?.wealthkernel?.status === "Matched")
            .reduce((sum, order) => sum.plus(order.consideration.amount), new Decimal(0));

          return holdingsAmountFromMatchedOrders.plus(transaction.consideration?.cashAmount ?? 0).toNumber();
        }
      })
      .reduce((sum, amount) => sum.plus(amount), new Decimal(0))
      .div(100);

    return new Decimal(dbCash)
      .add(withdrawalAmount)
      .add(rewardedAmount)
      .add(pendingChargeAmount)
      .add(stagnantSavingsDividendAmount)
      .toNumber();
  }

  private static async _getCashHoldingMismatchPrerequisites(portfolio: PortfolioDocument): Promise<{
    hasActiveTransactions: boolean;
    aggregatedDbCash: number;
  }> {
    const cashHoldingMismatchPrerequisites = getCachedDataWithFallback(
      `cashMismatchPrerequisites:${portfolio.id}`,
      async () => {
        const owner = portfolio.owner._id;

        const [pendingTransactions, pendingWithdrawals, pendingCharges, rewards, stagnantSavingsDividends] =
          await Promise.all([
            Transaction.find({
              owner,
              $or: [
                {
                  category: "AssetTransaction",
                  status: "Pending"
                },
                {
                  category: "RebalanceTransaction",
                  rebalanceStatus: { $in: ["PendingSell", "PendingBuy"] }
                }
              ]
            }),
            WithdrawalCashTransaction.find({
              owner,
              category: "WithdrawalCashTransaction",
              "providers.wealthkernel.status": { $nin: ["Settled", "Cancelled", "Rejected"] }
            }),
            ChargeTransaction.find({
              owner,
              chargeMethod: { $nin: ["direct-debit", "card"] },
              status: { $in: ["Pending", "PendingWealthkernelCharge"] }
            }).populate("orders"),
            Reward.find({
              targetUser: owner,
              "deposit.providers.wealthkernel.status": "Settled",
              $or: [
                { "order.providers.wealthkernel.id": { $exists: false } },
                { "order.providers.wealthkernel.status": "Pending" }
              ]
            }),
            SavingsDividendTransaction.find({
              owner,
              status: "PendingReinvestment",
              "consideration.amount": { $lt: Decimal.mul(SAVINGS_PRODUCT_MINIMUM_WK_ORDER_AMOUNT, 100).toNumber() }
            })
          ]);

        const hasActiveTransactions = pendingTransactions.length > 0;

        const aggregatedDbCash = DataVerificationService._aggregateDbCashFromRelevantTransactions(
          portfolio.cash[portfolio.currency].available,
          {
            pendingCharges,
            rewards,
            pendingWithdrawals,
            stagnantSavingsDividends
          }
        );

        return { hasActiveTransactions, aggregatedDbCash };
      },
      (_) => MISMATCH_PRE_REQUISITES_CACHE_EXPIRATION
    );

    return cashHoldingMismatchPrerequisites;
  }
}

export default DataVerificationService;
